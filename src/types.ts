export type RiskLevel = 'alto' | 'medio' | 'médio' | 'baixo'

export interface Pontuacao {
  fit_faang: number
  fit_consultoria: number
  robustez_metrica: number
  score_final: number
  justificativa: string
}

export interface PontosFortes {
  impacto: string[]
  lideranca: string[]
  tecnico_analitico: string[]
  negocio_estrategia: string[]
  diferenciais: string[]
}

export interface GapPrioritario {
  gap: string
  risco: RiskLevel
  efeito: string
  prioridade: number
}

export interface BulletReescrito {
  original: string
  problemas: string[]
  reescrito: string
  racional: string
}

export interface Recomendacoes {
  faang: string[]
  consultoria: string[]
  geral: string[]
}

export interface ResumeAnalysis {
  resumo_executivo: string
  pontuacao: Pontuacao
  pontos_fortes: PontosFortes
  gaps_prioritarios: GapPrioritario[]
  recomendacoes: Recomendacoes
  bullets_reescritos: BulletReescrito[]
  palavras_chave_sugeridas: string[]
  clarificacoes_pendentes: string[]
  observacoes_adicionais: string
}

