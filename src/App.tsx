import { useResume } from './context/ResumeContext'
import { AnalysisView } from './components/AnalysisView'
import { Triage } from './components/Triage'
import { Nav } from './components/Nav'
import { detectLocale, t } from './i18n'

function App() {
  const { data } = useResume()
  const labels = t(data ? detectLocale(data) : 'en-US')
  const navItems = data ? [
    { id: 'summary', label: labels.executiveSummary },
    { id: 'scoring', label: labels.scoring },
    { id: 'strengths', label: labels.strengths },
    { id: 'gaps', label: labels.gaps },
    { id: 'recommendations', label: labels.recommendations },
    { id: 'bullets', label: labels.bullets },
    { id: 'keywords', label: labels.keywords },
    { id: 'clarifications', label: labels.clarifications },
    { id: 'notes', label: labels.additionalNotes },
  ] : []

  return (
    <div className="min-h-screen">
      <Nav items={navItems} />
  <main id="main" className="container mx-auto py-6 md:py-10 space-y-8 md:space-y-12">
        {!data && <Triage onLoaded={() => { /* analysis will appear automatically */ }} />}
        {data && <AnalysisView data={data} />}
      </main>
    </div>
  )
}

export default App
