import { useResume } from './context/ResumeContext'
import { AnalysisView } from './components/AnalysisView'
import { Triage } from './components/Triage'
import { Nav } from './components/Nav'
import { detectLocale, t } from './i18n'
import { useMemo } from 'react'

function App() {
  const { data } = useResume()

  // Memoize locale detection and labels for performance
  const locale = useMemo(() => data ? detectLocale(data) : 'en-US', [data])
  const labels = useMemo(() => t(locale), [locale])

  // Memoize navigation items to prevent unnecessary re-renders
  const navItems = useMemo(() => data ? [
    { id: 'summary', label: labels.executiveSummary },
    { id: 'scoring', label: labels.scoring },
    { id: 'strengths', label: labels.strengths },
    { id: 'gaps', label: labels.gaps },
    { id: 'recommendations', label: labels.recommendations },
    { id: 'bullets', label: labels.bullets },
    { id: 'keywords', label: labels.keywords },
    { id: 'clarifications', label: labels.clarifications },
    { id: 'notes', label: labels.additionalNotes },
  ] : [], [data, labels])

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-100">
      <Nav items={navItems} />
      <main id="main" className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 lg:py-16 space-y-12 md:space-y-16 lg:space-y-20">
        {!data && <Triage onLoaded={() => { /* analysis will appear automatically */ }} />}
        {data && <AnalysisView data={data} />}
      </main>

      {/* Scroll to top button for mobile */}
      {data && (
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-6 right-6 w-12 h-12 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-40 md:hidden focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500/60 focus-visible:ring-offset-2"
          aria-label="Scroll to top"
        >
          <svg className="w-5 h-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </button>
      )}
    </div>
  )
}

export default App
