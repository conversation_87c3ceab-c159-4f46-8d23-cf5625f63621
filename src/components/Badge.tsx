type Props = {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'primary';
  size?: 'sm' | 'md' | 'lg';
}

export function Badge({ children, className = '', variant = 'default', size = 'md' }: Props) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return 'bg-success-50 text-success-700 border-success-200 ring-success-600/20'
      case 'warning':
        return 'bg-warning-50 text-warning-700 border-warning-200 ring-warning-600/20'
      case 'danger':
        return 'bg-danger-50 text-danger-700 border-danger-200 ring-danger-600/20'
      case 'primary':
        return 'bg-primary-50 text-primary-700 border-primary-200 ring-primary-600/20'
      default:
        return 'bg-neutral-50 text-neutral-700 border-neutral-200 ring-neutral-600/20'
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs'
      case 'lg':
        return 'px-4 py-2 text-sm'
      default:
        return 'px-3 py-1.5 text-sm'
    }
  }

  return (
    <span
      className={`inline-flex items-center gap-1.5 rounded-xl border font-semibold ring-1 ring-inset transition-all duration-200 ${getVariantClasses()} ${getSizeClasses()} ${className}`}
    >
      {children}
    </span>
  )
}

