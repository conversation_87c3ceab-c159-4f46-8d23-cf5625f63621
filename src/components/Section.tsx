type Props = { id: string; title: string; children: React.ReactNode; description?: string }

export function Section({ id, title, children, description }: Props) {
  return (
    <section id={id} className="scroll-mt-24">
      <header className="mb-4">
        <h2 className="text-xl md:text-2xl font-semibold tracking-tight text-gray-900">{title}</h2>
        {description && <p className="mt-1 text-sm text-gray-600">{description}</p>}
      </header>
  <div className="ra-card p-4 md:p-6 fade-in">{children}</div>
    </section>
  )
}

