type Props = { id: string; title: string; children: React.ReactNode; description?: string }

export function Section({ id, title, children, description }: Props) {
  return (
    <section id={id} className="scroll-mt-20">
      <header className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-1 h-8 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full"></div>
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight text-neutral-900">{title}</h2>
        </div>
        {description && (
          <p className="text-lg text-neutral-600 ml-7 max-w-3xl leading-relaxed">{description}</p>
        )}
      </header>

      <div className="ra-card elevated p-6 md:p-8 fade-in">
        {children}
      </div>
    </section>
  )
}

