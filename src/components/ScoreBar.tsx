type Props = { label: string; value: number; max?: number }

export function ScoreBar({ label, value, max = 10 }: Props) {
  const pct = Math.min(100, Math.max(0, (value / max) * 100))
  const displayValue = value.toFixed(1).replace('.0', '')

  // Enhanced color system based on performance
  const getPerformanceLevel = () => {
    if (pct >= 80) return {
      level: 'excellent',
      color: 'success',
      bgColor: 'bg-success-50',
      borderColor: 'border-success-200',
      textColor: 'text-success-700',
      strokeColor: '#059669', // success-600
      glowColor: 'shadow-success-500/20'
    }
    if (pct >= 60) return {
      level: 'good',
      color: 'warning',
      bgColor: 'bg-warning-50',
      borderColor: 'border-warning-200',
      textColor: 'text-warning-700',
      strokeColor: '#d97706', // warning-600
      glowColor: 'shadow-warning-500/20'
    }
    return {
      level: 'needs improvement',
      color: 'danger',
      bgColor: 'bg-danger-50',
      borderColor: 'border-danger-200',
      textColor: 'text-danger-700',
      strokeColor: '#dc2626', // danger-600
      glowColor: 'shadow-danger-500/20'
    }
  }

  const performance = getPerformanceLevel()
  const circumference = 2 * Math.PI * 45 // radius of 45
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (pct / 100) * circumference

  return (
    <div className={`ra-card p-6 ${performance.bgColor} ${performance.borderColor} hover:shadow-lg ${performance.glowColor} transition-all duration-300`} role="group" aria-label={label}>
      <div className="flex items-center gap-6">
        {/* Circular Progress Indicator */}
        <div className="relative flex-shrink-0">
          <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
            {/* Background circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="6"
              fill="none"
              className="text-neutral-200"
            />
            {/* Progress circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke={performance.strokeColor}
              strokeWidth="6"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className="transition-all duration-1000 ease-out"
              style={{
                filter: 'drop-shadow(0 0 6px rgba(0,0,0,0.1))'
              }}
            />
          </svg>

          {/* Score in center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className={`text-2xl font-bold ${performance.textColor}`}>
                {displayValue}
              </div>
              <div className="text-xs text-neutral-500 font-medium">
                / {max}
              </div>
            </div>
          </div>
        </div>

        {/* Label and Performance Info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-neutral-800 mb-2">{label}</h3>

          {/* Performance level badge */}
          <div className="flex items-center gap-2 mb-3">
            <div className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-sm font-medium ${performance.bgColor} ${performance.textColor} ${performance.borderColor} border`}>
              <div className={`w-2 h-2 rounded-full ${performance.color === 'success' ? 'bg-success-500' : performance.color === 'warning' ? 'bg-warning-500' : 'bg-danger-500'}`}></div>
              {performance.level}
            </div>
            <span className="text-sm text-neutral-500">({pct.toFixed(0)}%)</span>
          </div>

          {/* Performance description */}
          <div className="text-sm text-neutral-600">
            {pct >= 80 && "Outstanding performance - exceeds expectations"}
            {pct >= 60 && pct < 80 && "Good performance - meets most requirements"}
            {pct < 60 && "Room for improvement - focus area identified"}
          </div>
        </div>

        {/* Performance icon */}
        <div className="flex-shrink-0">
          <div className={`w-12 h-12 rounded-full ${performance.bgColor} ${performance.borderColor} border-2 flex items-center justify-center`}>
            {pct >= 80 && (
              <svg className="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
            {pct >= 60 && pct < 80 && (
              <svg className="w-6 h-6 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
            {pct < 60 && (
              <svg className="w-6 h-6 text-danger-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
          </div>
        </div>
      </div>

      {/* Hidden accessibility info */}
      <div
        className="sr-only"
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={`${label}: ${displayValue} out of ${max}, ${performance.level}`}
      />
    </div>
  )
}

