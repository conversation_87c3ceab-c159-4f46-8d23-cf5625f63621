type Props = { label: string; value: number; max?: number }

export function ScoreBar({ label, value, max = 10 }: Props) {
  const pct = Math.min(100, Math.max(0, (value / max) * 100))

  // Enhanced color system with better gradients
  const getColorClasses = () => {
    if (pct >= 80) return 'ra-progress-high'
    if (pct >= 60) return 'ra-progress-medium'
    return 'ra-progress-low'
  }

  const getScoreColor = () => {
    if (pct >= 80) return 'text-success-700'
    if (pct >= 60) return 'text-warning-700'
    return 'text-danger-700'
  }

  const displayValue = value.toFixed(1).replace('.0', '')

  return (
    <div className="space-y-3" role="group" aria-label={label}>
      <div className="flex items-center justify-between">
        <span className="text-base font-semibold text-neutral-800">{label}</span>
        <div className="flex items-center gap-2">
          <span className={`text-lg font-bold ${getScoreColor()}`} aria-hidden>
            {displayValue}
          </span>
          <span className="text-sm text-neutral-500">/ {max}</span>
        </div>
      </div>

      <div className="relative">
        <div
          className="relative h-3 w-full rounded-full bg-neutral-200 overflow-hidden shadow-inner"
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-label={`${label}: ${displayValue} out of ${max}`}
        >
          <div
            className={`h-full rounded-full ${getColorClasses()} transition-all duration-500 ease-out relative overflow-hidden`}
            style={{ width: `${pct}%` }}
          >
            {/* Subtle shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 animate-pulse"></div>
          </div>

          {/* Score text overlay for larger screens */}
          <div className="hidden md:flex absolute inset-0 items-center justify-center">
            <span className="text-xs font-bold text-white/90 drop-shadow-sm">
              {pct > 15 ? displayValue : ''}
            </span>
          </div>
        </div>

        {/* Performance indicator */}
        <div className="flex justify-between mt-2 text-xs text-neutral-500">
          <span>Poor</span>
          <span>Good</span>
          <span>Excellent</span>
        </div>
      </div>
    </div>
  )
}

