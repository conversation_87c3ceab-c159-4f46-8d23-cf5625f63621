type Props = { label: string; value: number; max?: number }

export function ScoreBar({ label, value, max = 10 }: Props) {
  const pct = Math.min(100, Math.max(0, (value / max) * 100))
  const base = pct >= 70 ? 'from-emerald-500 to-emerald-400' : pct >= 40 ? 'from-amber-500 to-amber-400' : 'from-rose-500 to-rose-400'
  return (
    <div className="space-y-1" role="group" aria-label={label}>
      <div className="flex items-baseline justify-between">
        <span className="text-sm font-medium text-gray-800">{label}</span>
        <span className="text-xs text-gray-500" aria-hidden>{value.toFixed(1).replace('.0','')}</span>
      </div>
      <div
        className="relative h-2 w-full rounded-full bg-gray-200 overflow-hidden"
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={label}
      >
        <div
          className={`h-full rounded-full bg-gradient-to-r ${base} transition-all duration-300`}
          style={{ width: `${pct}%` }}
        />
        <span className="hidden md:block absolute inset-0 text-[10px] leading-[0.5rem] tracking-wide text-white/90 font-medium pl-1 select-none">
          {value.toFixed(1).replace('.0','')}
        </span>
      </div>
    </div>
  )
}

