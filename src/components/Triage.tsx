import { useRef, useState, useCallback, useMemo, useEffect } from 'react'
import { useResume } from '../context/ResumeContext'
import { clearRaw } from '../utils/storage'

interface UploadState {
  phase: 'idle' | 'reading' | 'parsing' | 'validating' | 'ready' | 'error'
  errors: string[]
  fileName?: string
  size?: number
  raw?: string
}

export function Triage({ onLoaded }: { onLoaded: () => void }) {
  const inputRef = useRef<HTMLInputElement | null>(null)
  const { loadFromString } = useResume()
  const [st, setSt] = useState<UploadState>({ phase: 'idle', errors: [] })
  const [showPreview, setShowPreview] = useState(false)
  const [warnings, setWarnings] = useState<string[]>([])
  const [previousRaw, setPreviousRaw] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)

  useEffect(() => {
    const stored = localStorage.getItem('resume_raw')
    if (stored) setPreviousRaw(stored)
    const onDrag = (e: DragEvent) => {
      e.preventDefault(); e.stopPropagation()
      if (e.type === 'dragenter' || e.type === 'dragover') setDragActive(true)
      if (e.type === 'dragleave') setDragActive(false)
    }
    const onDropGlobal = (e: DragEvent) => {
      e.preventDefault(); e.stopPropagation(); setDragActive(false)
      const f = e.dataTransfer?.files?.[0]
      if (f) handleFiles(f)
    }
    window.addEventListener('dragenter', onDrag)
    window.addEventListener('dragover', onDrag)
    window.addEventListener('dragleave', onDrag)
    window.addEventListener('drop', onDropGlobal)
    return () => {
      window.removeEventListener('dragenter', onDrag)
      window.removeEventListener('dragover', onDrag)
      window.removeEventListener('dragleave', onDrag)
      window.removeEventListener('drop', onDropGlobal)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleFiles = useCallback((file: File) => {
    if (!file.name.endsWith('.json')) {
      setSt({ phase: 'error', errors: ['File must have .json extension'] })
      return
    }
    if (file.size > 2 * 1024 * 1024) {
      setSt({ phase: 'error', errors: ['File too large (>2MB)'] })
      return
    }
    setSt({ phase: 'reading', errors: [], fileName: file.name, size: file.size })
    const reader = new FileReader()
    reader.onload = () => {
      const txt = reader.result as string
  setSt((s) => ({ ...s, phase: 'parsing', raw: txt }))
	const res = loadFromString(txt)
      if (!res.ok) {
        setSt((s) => ({ ...s, phase: 'error', errors: res.errors || [] }))
      } else {
        setWarnings(res.warnings || [])
        setSt((s) => ({ ...s, phase: 'ready', errors: [] }))
    setPreviousRaw(localStorage.getItem('resume_raw'))
        onLoaded()
      }
    }
    reader.onerror = () => {
      setSt({ phase: 'error', errors: ['Failed reading file'] })
    }
    reader.readAsText(file)
  }, [loadFromString, onLoaded])

  const onDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const f = e.dataTransfer.files?.[0]
    if (f) handleFiles(f)
  }
  const onBrowse = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0]
    if (f) handleFiles(f)
  }

  const presence = useMemo(() => {
    if (!st.raw) return [] as { key: string; ok: boolean }[]
    try {
      const parsed = JSON.parse(st.raw)
      const keys = ['resumo_executivo','pontuacao','pontos_fortes','gaps_prioritarios','recomendacoes','bullets_reescritos','palavras_chave_sugeridas','clarificacoes_pendentes','observacoes_adicionais']
      return keys.map(k => ({ key: k, ok: k in parsed }))
    } catch { return [] }
  }, [st.raw])

  return (
    <div className="max-w-3xl mx-auto py-12 space-y-8">
      {dragActive && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-primary-600/90 backdrop-blur-sm text-white pointer-events-none select-none fade-in">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto rounded-full bg-white/20 flex items-center justify-center">
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <div className="text-2xl font-semibold">Drop JSON to load</div>
            <div className="text-white/80">Release to upload your analysis file</div>
          </div>
        </div>
      )}

      <div className="text-center space-y-4">
        <div className="w-20 h-20 mx-auto rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center shadow-lg">
          <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-neutral-900">Upload Resume Analysis</h1>
        <p className="text-lg text-neutral-600 max-w-2xl mx-auto">Drop a JSON file exported from your analysis pipeline. We'll validate the structure and render it beautifully.</p>
      </div>

      <div
        onDrop={onDrop}
        onDragOver={(e) => { e.preventDefault(); e.dataTransfer.dropEffect = 'copy' }}
        className={`ra-card elevated border-2 border-dashed p-12 text-center flex flex-col items-center gap-6 transition-all duration-300 ${
          dragActive
            ? 'border-primary-400 bg-primary-50 scale-105'
            : 'border-neutral-300 hover:border-primary-300 hover:bg-primary-50/50'
        }`}
      >
        <div className="w-16 h-16 rounded-full bg-neutral-100 flex items-center justify-center">
          <svg className="w-8 h-8 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        </div>

        <div className="space-y-2">
          <p className="text-lg font-medium text-neutral-700">Drag & drop your <code className="px-2 py-1 bg-neutral-100 rounded text-primary-600 font-mono text-sm">.json</code> file here</p>
          <p className="text-neutral-500">Maximum file size: 2MB</p>
        </div>

        <div className="flex items-center gap-4">
          <div className="h-px bg-neutral-300 flex-1"></div>
          <span className="text-sm text-neutral-500 font-medium">OR</span>
          <div className="h-px bg-neutral-300 flex-1"></div>
        </div>

        <button
          onClick={() => inputRef.current?.click()}
          className="ra-btn ra-btn-primary px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z" />
          </svg>
          Browse Files
        </button>

        <input data-testid="hidden-input" ref={inputRef} type="file" accept=".json,application/json" hidden onChange={onBrowse} />
      </div>
      {st.fileName && (
        <div className="ra-card p-4 flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-primary-100 flex items-center justify-center">
            <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div>
            <div className="font-medium text-neutral-900">{st.fileName}</div>
            <div className="text-sm text-neutral-500">{Math.round((st.size||0)/1024)} KB</div>
          </div>
        </div>
      )}

      {st.phase === 'reading' && <Status msg="Reading file..." icon="upload" />}
      {st.phase === 'parsing' && <Status msg="Parsing & validating..." icon="cog" />}

      {st.phase === 'error' && (
        <div className="ra-card border-danger-200 bg-danger-50 p-6 space-y-4" role="alert" aria-live="assertive">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-danger-100 flex items-center justify-center">
              <svg className="w-5 h-5 text-danger-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <div className="font-semibold text-danger-800">Validation Failed</div>
              <div className="text-sm text-danger-700">Please check your file and try again</div>
            </div>
          </div>
          <ul className="space-y-2">
            {st.errors.map((e,i)=> (
              <li key={i} className="flex items-start gap-2 text-sm text-danger-700">
                <div className="w-1.5 h-1.5 rounded-full bg-danger-400 mt-2 flex-shrink-0"></div>
                {e}
              </li>
            ))}
          </ul>
          <button
            className="ra-btn ra-btn-secondary text-danger-700 border-danger-300 hover:bg-danger-100"
            onClick={() => setSt({ phase: 'idle', errors: [] })}
          >
            Try Again
          </button>
        </div>
      )}

      {st.phase === 'ready' && (
        <div className="ra-card border-success-200 bg-success-50 p-6">
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 rounded-full bg-success-100 flex items-center justify-center">
                  <svg className="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <div className="font-semibold text-success-800">Analysis Loaded Successfully</div>
                  <div className="text-sm text-success-700">Scroll down to view the rendered sections</div>
                </div>
              </div>

              {warnings.length > 0 && (
                <div className="mt-4 p-3 bg-warning-50 border border-warning-200 rounded-lg">
                  <div className="font-medium text-warning-800 mb-2">Warnings</div>
                  <ul className="space-y-1">
                    {warnings.map((w,i)=>(
                      <li key={i} className="flex items-start gap-2 text-sm text-warning-700">
                        <div className="w-1.5 h-1.5 rounded-full bg-warning-400 mt-2 flex-shrink-0"></div>
                        {w}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {previousRaw && (
                <div className="mt-4 p-3 bg-neutral-100 rounded-lg">
                  <div className="text-sm text-neutral-600">
                    <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Previous analysis detected in storage
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={() => { clearRaw(); setWarnings([]); setSt({ phase: 'idle', errors: [] }); }}
              className="ra-btn ra-btn-secondary text-success-700 border-success-300 hover:bg-success-100"
            >
              Clear & Upload New
            </button>
          </div>
        </div>
      )}
      {st.raw && (
        <div className="space-y-6">
          <div className="ra-card p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-neutral-900">Schema Validation</h3>
              <button
                className="ra-btn ra-btn-secondary text-sm"
                onClick={() => setShowPreview(v=>!v)}
              >
                {showPreview ? (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l-1.415-1.414M14.12 14.12l1.415 1.415M14.12 14.12L15.536 15.536M14.12 14.12l1.414 1.414" />
                    </svg>
                    Hide JSON
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Show JSON
                  </>
                )}
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {presence.map(p => (
                <div key={p.key} className={`flex items-center gap-3 p-3 rounded-xl border ${
                  p.ok
                    ? 'bg-success-50 border-success-200 text-success-800'
                    : 'bg-danger-50 border-danger-200 text-danger-800'
                }`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    p.ok ? 'bg-success-100' : 'bg-danger-100'
                  }`}>
                    {p.ok ? (
                      <svg className="w-3 h-3 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-3 h-3 text-danger-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <span className="text-sm font-medium font-mono">{p.key}</span>
                </div>
              ))}
            </div>

            {showPreview && (
              <div className="mt-6">
                <div className="bg-neutral-900 rounded-xl p-4 max-h-80 overflow-auto">
                  <pre className="text-xs leading-relaxed text-neutral-100 font-mono">
{st.raw}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="ra-card p-4 bg-neutral-50">
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 rounded-full bg-neutral-200 flex items-center justify-center flex-shrink-0 mt-0.5">
            <svg className="w-3 h-3 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="text-sm text-neutral-600">
            <div className="font-medium mb-1">Required JSON Structure</div>
            <div className="font-mono text-xs">resumo_executivo, pontuacao, pontos_fortes, gaps_prioritarios, recomendacoes, bullets_reescritos, palavras_chave_sugeridas, clarificacoes_pendentes, observacoes_adicionais</div>
          </div>
        </div>
      </div>
      {st.phase === 'ready' && (
        <div className="ra-card p-6">
          <h3 className="text-lg font-semibold text-neutral-900 mb-4">Quick Actions</h3>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => {
                const raw = localStorage.getItem('resume_raw')
                if (!raw) return
                const blob = new Blob([raw], { type: 'application/json' })
                const a = document.createElement('a')
                a.href = URL.createObjectURL(blob)
                a.download = 'resume-analysis.json'
                a.click()
                URL.revokeObjectURL(a.href)
              }}
              className="ra-btn ra-btn-secondary flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download JSON
            </button>
            <button
              onClick={async () => {
                const raw = localStorage.getItem('resume_raw')
                if (!raw) return
                try {
                  const encoded = btoa(encodeURIComponent(raw))
                  const url = `${location.origin}${location.pathname}#data=${encoded}`
                  await navigator.clipboard.writeText(url)
                  // You could replace this alert with a toast notification
                  alert('Shareable URL copied to clipboard!')
                } catch (error) {
                  console.error('Failed to copy URL:', error)
                  alert('Failed to copy URL to clipboard')
                }
              }}
              className="ra-btn ra-btn-primary flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Copy Share URL
            </button>
          </div>
          <div className="mt-4 p-3 bg-neutral-100 rounded-lg">
            <div className="text-sm text-neutral-600">
              <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Share URLs encode your analysis data for easy sharing. No server upload required.
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

function Status({ msg, icon = "pulse" }: { msg: string; icon?: string }) {
  const getIcon = () => {
    switch (icon) {
      case "upload":
        return (
          <svg className="w-5 h-5 text-primary-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        )
      case "cog":
        return (
          <svg className="w-5 h-5 text-primary-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        )
      default:
        return <span className="inline-block h-3 w-3 rounded-full bg-primary-500 animate-pulse" aria-hidden />
    }
  }

  return (
    <div className="ra-card p-4 flex items-center gap-3" role="status" aria-live="polite">
      <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
        {getIcon()}
      </div>
      <div className="font-medium text-neutral-700">{msg}</div>
    </div>
  )
}
