import { useRef, useState, useCallback, useMemo, useEffect } from 'react'
import { useResume } from '../context/ResumeContext'
import { clearRaw } from '../utils/storage'

interface UploadState {
  phase: 'idle' | 'reading' | 'parsing' | 'validating' | 'ready' | 'error'
  errors: string[]
  fileName?: string
  size?: number
  raw?: string
}

export function Triage({ onLoaded }: { onLoaded: () => void }) {
  const inputRef = useRef<HTMLInputElement | null>(null)
  const { loadFromString } = useResume()
  const [st, setSt] = useState<UploadState>({ phase: 'idle', errors: [] })
  const [showPreview, setShowPreview] = useState(false)
  const [warnings, setWarnings] = useState<string[]>([])
  const [previousRaw, setPreviousRaw] = useState<string | null>(null)
  const [dragActive, setDragActive] = useState(false)

  useEffect(() => {
    const stored = localStorage.getItem('resume_raw')
    if (stored) setPreviousRaw(stored)
    const onDrag = (e: DragEvent) => {
      e.preventDefault(); e.stopPropagation()
      if (e.type === 'dragenter' || e.type === 'dragover') setDragActive(true)
      if (e.type === 'dragleave') setDragActive(false)
    }
    const onDropGlobal = (e: DragEvent) => {
      e.preventDefault(); e.stopPropagation(); setDragActive(false)
      const f = e.dataTransfer?.files?.[0]
      if (f) handleFiles(f)
    }
    window.addEventListener('dragenter', onDrag)
    window.addEventListener('dragover', onDrag)
    window.addEventListener('dragleave', onDrag)
    window.addEventListener('drop', onDropGlobal)
    return () => {
      window.removeEventListener('dragenter', onDrag)
      window.removeEventListener('dragover', onDrag)
      window.removeEventListener('dragleave', onDrag)
      window.removeEventListener('drop', onDropGlobal)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleFiles = useCallback((file: File) => {
    if (!file.name.endsWith('.json')) {
      setSt({ phase: 'error', errors: ['File must have .json extension'] })
      return
    }
    if (file.size > 2 * 1024 * 1024) {
      setSt({ phase: 'error', errors: ['File too large (>2MB)'] })
      return
    }
    setSt({ phase: 'reading', errors: [], fileName: file.name, size: file.size })
    const reader = new FileReader()
    reader.onload = () => {
      const txt = reader.result as string
  setSt((s) => ({ ...s, phase: 'parsing', raw: txt }))
	const res = loadFromString(txt)
      if (!res.ok) {
        setSt((s) => ({ ...s, phase: 'error', errors: res.errors || [] }))
      } else {
        setWarnings(res.warnings || [])
        setSt((s) => ({ ...s, phase: 'ready', errors: [] }))
    setPreviousRaw(localStorage.getItem('resume_raw'))
        onLoaded()
      }
    }
    reader.onerror = () => {
      setSt({ phase: 'error', errors: ['Failed reading file'] })
    }
    reader.readAsText(file)
  }, [loadFromString, onLoaded])

  const onDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const f = e.dataTransfer.files?.[0]
    if (f) handleFiles(f)
  }
  const onBrowse = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0]
    if (f) handleFiles(f)
  }

  const presence = useMemo(() => {
    if (!st.raw) return [] as { key: string; ok: boolean }[]
    try {
      const parsed = JSON.parse(st.raw)
      const keys = ['resumo_executivo','pontuacao','pontos_fortes','gaps_prioritarios','recomendacoes','bullets_reescritos','palavras_chave_sugeridas','clarificacoes_pendentes','observacoes_adicionais']
      return keys.map(k => ({ key: k, ok: k in parsed }))
    } catch { return [] }
  }, [st.raw])

  return (
    <div className="max-w-2xl mx-auto py-10 space-y-6">
      {dragActive && (
        <div className="fixed inset-0 z-40 flex items-center justify-center bg-emerald-600/80 text-white text-xl font-medium pointer-events-none select-none">
          Drop JSON to load
        </div>
      )}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-semibold tracking-tight">Upload Resume Analysis JSON</h1>
        <p className="text-sm text-gray-600">Drop a JSON file exported from your analysis pipeline. We'll validate structure and render it.</p>
      </div>
      <div
        onDrop={onDrop}
        onDragOver={(e) => { e.preventDefault(); e.dataTransfer.dropEffect = 'copy' }}
        className="border-2 border-dashed rounded-xl p-10 text-center flex flex-col items-center gap-4 bg-gray-50 hover:bg-gray-100 transition-colors"
      >
        <p className="text-sm text-gray-700">Drag & drop your <code>.json</code> file here</p>
        <p className="text-xs text-gray-500">or</p>
        <button
          onClick={() => inputRef.current?.click()}
          className="px-4 py-2 rounded-md bg-gray-900 text-white text-sm font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900"
        >
          Browse files
        </button>
  <input data-testid="hidden-input" ref={inputRef} type="file" accept=".json,application/json" hidden onChange={onBrowse} />
      </div>
      {st.fileName && (
        <div className="text-sm text-gray-600">Selected: {st.fileName} ({Math.round((st.size||0)/1024)} KB)</div>
      )}
  {st.phase === 'reading' && <Status msg="Reading file..." />}
  {st.phase === 'parsing' && <Status msg="Parsing & validating..." />}
      {st.phase === 'error' && (
        <div className="rounded-lg border border-red-300 bg-red-50 p-4 space-y-2" role="alert" aria-live="assertive">
          <div className="font-medium text-red-700">Validation errors</div>
            <ul className="list-disc pl-5 text-xs text-red-700 space-y-1">
              {st.errors.map((e,i)=> <li key={i}>{e}</li>)}
            </ul>
          <button
            className="mt-2 text-xs text-red-700 underline"
            onClick={() => setSt({ phase: 'idle', errors: [] })}
          >Try again</button>
        </div>
      )}
      {st.phase === 'ready' && (
        <div className="rounded-lg border border-emerald-300 bg-emerald-50 p-4 flex items-start justify-between gap-4">
          <div>
            <div className="font-medium text-emerald-800">Analysis loaded</div>
            <p className="text-xs text-emerald-700">Scroll to view rendered sections below. You can upload another file anytime.</p>
            {warnings.length > 0 && (
              <ul className="mt-2 list-disc pl-4 space-y-1 text-[11px] text-amber-700">
                {warnings.map((w,i)=>(<li key={i}>{w}</li>))}
              </ul>
            )}
            {previousRaw && (
              <div className="mt-3 text-[11px] text-gray-600">
                Previous raw JSON detected. Diff view coming soon.
              </div>
            )}
          </div>
          <button onClick={() => { clearRaw(); setWarnings([]); setSt({ phase: 'idle', errors: [] }); }} className="text-xs text-emerald-700 underline">Clear</button>
        </div>
      )}
      {st.raw && (
        <div className="space-y-3">
          <button
            className="text-xs text-gray-600 underline"
            onClick={() => setShowPreview(v=>!v)}
          >{showPreview ? 'Hide' : 'Show'} raw JSON preview</button>
          <div className="grid grid-cols-2 gap-2">
            {presence.map(p => (
              <div key={p.key} className={`flex items-center gap-1 text-[11px] rounded px-2 py-1 ${p.ok ? 'bg-emerald-50 text-emerald-700' : 'bg-rose-50 text-rose-700'}`}>
                <span className={`h-2 w-2 rounded-full ${p.ok ? 'bg-emerald-500' : 'bg-rose-500'}`} /> {p.key}
              </div>
            ))}
          </div>
          {showPreview && (
            <pre className="max-h-72 overflow-auto text-[11px] leading-snug bg-gray-900 text-gray-100 p-3 rounded-lg border border-gray-700">
{st.raw}
            </pre>
          )}
        </div>
      )}
      <div className="text-xs text-gray-400">Structure required: resumo_executivo, pontuacao, pontos_fortes, gaps_prioritarios, recomendacoes, bullets_reescritos, palavras_chave_sugeridas, clarificacoes_pendentes, observacoes_adicionais.</div>
      {st.phase === 'ready' && (
        <div className="flex flex-wrap gap-2 pt-2">
          <button
            onClick={() => {
              const raw = localStorage.getItem('resume_raw')
              if (!raw) return
              const blob = new Blob([raw], { type: 'application/json' })
              const a = document.createElement('a')
              a.href = URL.createObjectURL(blob)
              a.download = 'resume-analysis.json'
              a.click()
              URL.revokeObjectURL(a.href)
            }}
            className="px-3 py-1 rounded-md bg-gray-900 text-white text-xs font-medium hover:bg-gray-800 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/60"
          >Download JSON</button>
          <button
            onClick={() => {
              const raw = localStorage.getItem('resume_raw')
              if (!raw) return
              const encoded = btoa(unescape(encodeURIComponent(raw)))
              const url = `${location.origin}${location.pathname}#data=${encoded}`
              navigator.clipboard.writeText(url)
              alert('Shareable URL copied to clipboard')
            }}
            className="px-3 py-1 rounded-md bg-emerald-600 text-white text-xs font-medium hover:bg-emerald-500 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/60"
          >Copy Share URL</button>
        </div>
      )}
    </div>
  )
}

function Status({ msg }: { msg: string }) {
  return (
    <div className="flex items-center gap-2 text-sm text-gray-600" role="status" aria-live="polite">
      <span className="inline-block h-2 w-2 rounded-full bg-emerald-500 animate-pulse" aria-hidden />
      {msg}
    </div>
  )
}
