type NavItem = { id: string; label: string }

import { useEffect, useState } from 'react'

export function Nav({ items }: { items: NavItem[] }) {
  const [open, setOpen] = useState(false)
  const [active, setActive] = useState<string | null>(null)

  useEffect(() => {
    if (!items.length) return
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) setActive(entry.target.id)
        })
      },
      { rootMargin: '-40% 0px -55% 0px', threshold: [0, 1] }
    )
    items.forEach(it => {
      const el = document.getElementById(it.id)
      if (el) observer.observe(el)
    })
    return () => observer.disconnect()
  }, [items])
  return (
    <nav aria-label="Primary" className="sticky top-0 z-30 w-full border-b border-neutral-200/60 bg-white/90 backdrop-blur-xl supports-[backdrop-filter]:bg-white/80 shadow-sm">
      <a href="#main" className="sr-only focus:not-sr-only focus:absolute focus:top-3 focus:left-3 bg-primary-600 text-white px-4 py-2 rounded-xl text-sm font-medium shadow-lg">Skip to content</a>
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-between h-16 gap-6">
          <div className="flex items-center gap-4 min-w-0">
            <a href="/" className="flex items-center gap-3 font-semibold text-neutral-900 whitespace-nowrap group">
              <div className="flex h-9 w-9 rounded-xl bg-gradient-to-br from-primary-600 to-primary-700 text-white text-sm items-center justify-center font-bold shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                RA
              </div>
              <span className="hidden sm:inline text-lg tracking-tight font-semibold bg-gradient-to-r from-neutral-900 to-neutral-700 bg-clip-text text-transparent">Resume Analyzer</span>
            </a>
            <div className="hidden md:flex items-center">
              <div className="w-px h-6 bg-neutral-200 mx-4"></div>
              <button
                onClick={(e)=>{e.preventDefault(); window.scrollTo({ top:0, behavior:'smooth'} )}}
                aria-label="Upload new analysis"
                className="inline-flex items-center gap-2 text-sm font-medium text-neutral-600 hover:text-primary-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500/60 rounded-lg px-3 py-2 transition-all duration-200 hover:bg-neutral-50"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span>Upload New</span>
              </button>
            </div>
          </div>
          <div className="flex md:hidden shrink-0">
            {items.length > 0 && (
              <button
                aria-label="Toggle navigation"
                onClick={() => setOpen(o => !o)}
                className="p-3 rounded-xl hover:bg-neutral-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500/60 transition-all duration-200 relative"
              >
                <div className="w-5 h-5 flex flex-col justify-center items-center">
                  <span className={`block w-5 h-0.5 bg-neutral-700 transition-all duration-300 ${open ? 'rotate-45 translate-y-0.5' : 'mb-1'}`} />
                  <span className={`block w-5 h-0.5 bg-neutral-700 transition-all duration-300 ${open ? 'opacity-0' : 'mb-1'}`} />
                  <span className={`block w-5 h-0.5 bg-neutral-700 transition-all duration-300 ${open ? '-rotate-45 -translate-y-0.5' : ''}`} />
                </div>
              </button>
            )}
          </div>
          {items.length > 0 && (
            <div className="hidden md:flex items-center gap-2">
              {items.map((it) => {
                const isActive = active === it.id
                return (
                  <a
                    key={it.id}
                    href={`#${it.id}`}
                    aria-current={isActive ? 'true' : undefined}
                    className={`text-sm font-medium px-4 py-2.5 rounded-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500/60 transition-all duration-200 relative overflow-hidden ${
                      isActive
                        ? 'bg-primary-600 text-white shadow-lg shadow-primary-600/25'
                        : 'text-neutral-600 hover:text-primary-600 hover:bg-primary-50 hover:shadow-sm'
                    }`}
                  >
                    <span className="relative z-10">{it.label}</span>
                    {isActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-primary-700 opacity-100"></div>
                    )}
                  </a>
                )
              })}
            </div>
          )}
        </div>
        {open && items.length > 0 && (
          <div className="md:hidden pb-4 pt-2 border-t border-neutral-200/60 mt-4 slide-in-right">
            <div className="flex flex-col gap-2">
              {items.map(it => {
                const isActive = active === it.id
                return (
                  <a
                    key={it.id}
                    href={`#${it.id}`}
                    onClick={() => setOpen(false)}
                    aria-current={isActive ? 'true' : undefined}
                    className={`text-sm font-medium px-4 py-3 rounded-xl focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500/60 transition-all duration-200 ${
                      isActive
                        ? 'bg-primary-600 text-white shadow-lg shadow-primary-600/25'
                        : 'bg-neutral-100 text-neutral-700 hover:bg-primary-50 hover:text-primary-600 active:bg-primary-100'
                    }`}
                  >
                    <span className="flex items-center justify-between">
                      {it.label}
                      {isActive && (
                        <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </span>
                  </a>
                )
              })}
            </div>
            <div className="mt-4 pt-4 border-t border-neutral-200/60">
              <button
                onClick={(e) => {
                  e.preventDefault()
                  window.scrollTo({ top: 0, behavior: 'smooth' })
                  setOpen(false)
                }}
                className="w-full inline-flex items-center justify-center gap-2 text-sm font-medium text-neutral-600 hover:text-primary-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500/60 rounded-xl px-4 py-3 transition-all duration-200 hover:bg-neutral-50"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span>Upload New Analysis</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

