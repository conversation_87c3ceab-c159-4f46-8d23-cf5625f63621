type NavItem = { id: string; label: string }

import { useEffect, useState } from 'react'

export function Nav({ items }: { items: NavItem[] }) {
  const [open, setOpen] = useState(false)
  const [active, setActive] = useState<string | null>(null)

  useEffect(() => {
    if (!items.length) return
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) setActive(entry.target.id)
        })
      },
      { rootMargin: '-40% 0px -55% 0px', threshold: [0, 1] }
    )
    items.forEach(it => {
      const el = document.getElementById(it.id)
      if (el) observer.observe(el)
    })
    return () => observer.disconnect()
  }, [items])
  return (
    <nav aria-label="Primary" className="sticky top-0 z-30 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm supports-[backdrop-filter]:bg-white/60 shadow-sm">
      <a href="#main" className="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-gray-900 text-white px-3 py-2 rounded-md text-sm">Skip to content</a>
      <div className="container mx-auto px-3 md:px-4">
        <div className="flex items-center justify-between h-14 gap-4">
          <div className="flex items-center gap-2 min-w-0">
            <a href="/" className="flex items-center gap-2 font-semibold text-gray-900 whitespace-nowrap">
              <span className="flex h-7 w-7 rounded-md bg-gray-900 text-white text-[11px] items-center justify-center font-bold shadow-inner">RA</span>
              <span className="hidden sm:inline text-sm tracking-tight">Resume Analyzer</span>
            </a>
            <button
              onClick={(e)=>{e.preventDefault(); window.scrollTo({ top:0, behavior:'smooth'} )}}
              aria-label="Upload new analysis"
              className="hidden sm:inline-flex items-center gap-1 text-xs font-medium text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/60 rounded px-2 py-1 transition-colors"
            >
              <span>Upload</span>
            </button>
          </div>
          <div className="flex md:hidden shrink-0">
            {items.length>0 && (
              <button aria-label="Toggle navigation" onClick={()=>setOpen(o=>!o)} className="p-2.5 rounded-md hover:bg-gray-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/60 transition-colors">
                <span className="block w-5 h-0.5 bg-gray-800 mb-1" />
                <span className="block w-5 h-0.5 bg-gray-800 mb-1" />
                <span className="block w-5 h-0.5 bg-gray-800" />
              </button>
            )}
          </div>
          {items.length > 0 && (
            <div className="hidden md:flex items-center gap-1.5">
              {items.map((it) => {
                const is = active === it.id
                return (
                  <a
                    key={it.id}
                    href={`#${it.id}`}
                    aria-current={is ? 'true' : undefined}
                    className={`text-[11px] tracking-wide px-2.5 py-1.5 rounded-md font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/60 transition-colors ${is ? 'bg-gray-900 text-white shadow-sm' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'} `}
                  >
                    <span className="inline-block align-middle">{it.label}</span>
                  </a>
                )
              })}
            </div>
          )}
        </div>
        {open && items.length>0 && (
          <div className="md:hidden pb-3 flex flex-wrap gap-2 pt-1">
            {items.map(it => {
              const is = active === it.id
              return (
                <a
                  key={it.id}
                  href={`#${it.id}`}
                  onClick={()=>setOpen(false)}
                  aria-current={is ? 'true' : undefined}
                  className={`text-[11px] px-2.5 py-1.5 rounded font-medium focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/60 transition-colors ${is ? 'bg-gray-900 text-white shadow-sm' : 'bg-gray-100 text-gray-700 active:bg-gray-200'}`}
                >
                  {it.label}
                </a>
              )
            })}
          </div>
        )}
      </div>
    </nav>
  )
}

