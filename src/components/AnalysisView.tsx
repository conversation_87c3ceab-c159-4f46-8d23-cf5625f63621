import { useMemo } from 'react'
import { Section } from './Section'
import { ScoreBar } from './ScoreBar'
import { Badge } from './Badge'
import { detectLocale, t } from '../i18n'
import type { ResumeAnalysis } from '../types'

export function AnalysisView({ data }: { data: ResumeAnalysis }) {
  const locale = useMemo(() => detectLocale(data), [data])
  const labels = useMemo(() => t(locale), [locale])

  return (
    <div className="space-y-12">
      <Section id="summary" title={labels.executiveSummary}>
        <div className="prose prose-lg max-w-none">
          <div className="bg-gradient-to-r from-primary-50 to-primary-100/50 rounded-2xl p-6 border border-primary-200/50">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-xl bg-primary-100 flex items-center justify-center flex-shrink-0">
                <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-primary-900 mb-3">Executive Summary</h3>
                <p className="text-neutral-800 leading-relaxed whitespace-pre-line text-base">{data.resumo_executivo}</p>
              </div>
            </div>
          </div>
        </div>
      </Section>

      <Section id="scoring" title={labels.scoring}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <ScoreBar label={labels.scores.fit_faang} value={data.pontuacao.fit_faang} />
            <ScoreBar label={labels.scores.fit_consultoria} value={data.pontuacao.fit_consultoria} />
            <ScoreBar label={labels.scores.robustez_metrica} value={data.pontuacao.robustez_metrica} />
            <ScoreBar label={labels.scores.score_final} value={data.pontuacao.score_final} max={10} />
          </div>

          <div className="lg:col-span-1">
            <div className="ra-card bg-gradient-to-br from-neutral-50 to-neutral-100 p-6 h-full">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 rounded-xl bg-primary-100 flex items-center justify-center">
                  <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <div className="text-lg font-bold text-neutral-900">
                    {labels.scores.score_final}: {data.pontuacao.score_final.toFixed(1)}/10
                  </div>
                  <div className="text-sm text-neutral-600">Overall Assessment</div>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-neutral-800">Analysis</h4>
                <p className="text-neutral-700 leading-relaxed">{data.pontuacao.justificativa}</p>
              </div>
            </div>
          </div>
        </div>
      </Section>

      <Section id="strengths" title={labels.strengths}>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {(['impacto','lideranca','tecnico_analitico','negocio_estrategia','diferenciais'] as const).map((key) => {
            const items = data.pontos_fortes[key]
            const icons = {
              impacto: "M13 10V3L4 14h7v7l9-11h-7z",
              lideranca: "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
              tecnico_analitico: "M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z",
              negocio_estrategia: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
              diferenciais: "M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
            }

            return (
              <div key={key} className="ra-card p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 rounded-xl bg-success-100 flex items-center justify-center">
                    <svg className="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={icons[key]} />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-800">{labels.categories[key]}</h3>
                </div>
                <ul className="space-y-3">
                  {items.map((it: string, idx: number) => (
                    <li key={idx} className="flex items-start gap-3">
                      <div className="w-2 h-2 rounded-full bg-success-400 mt-2 flex-shrink-0"></div>
                      <span className="text-neutral-700 leading-relaxed">{it}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )
          })}
        </div>
      </Section>

      <Section id="gaps" title={labels.gaps}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {data.gaps_prioritarios
            .slice()
            .sort((a, b) => a.prioridade - b.prioridade)
            .map((g, idx) => {
              const getRiskVariant = (risk: string) => {
                const r = risk.toLowerCase()
                if (r.includes('alto')) return 'danger'
                if (r.includes('med')) return 'warning'
                return 'success'
              }

              const getRiskIcon = (risk: string) => {
                const r = risk.toLowerCase()
                if (r.includes('alto')) return "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                if (r.includes('med')) return "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                return "M5 13l4 4L19 7"
              }

              return (
                <div key={idx} className="ra-card p-6 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-start justify-between gap-4 mb-4">
                    <div className="flex items-start gap-3 flex-1">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                        getRiskVariant(g.risco) === 'danger' ? 'bg-danger-100' :
                        getRiskVariant(g.risco) === 'warning' ? 'bg-warning-100' : 'bg-success-100'
                      }`}>
                        <svg className={`w-5 h-5 ${
                          getRiskVariant(g.risco) === 'danger' ? 'text-danger-600' :
                          getRiskVariant(g.risco) === 'warning' ? 'text-warning-600' : 'text-success-600'
                        }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={getRiskIcon(g.risco)} />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-neutral-900 mb-2">{g.gap}</h4>
                        <div className="space-y-2">
                          <div className="flex items-start gap-2">
                            <span className="text-sm font-medium text-neutral-600 min-w-0">{labels.effect}:</span>
                            <span className="text-sm text-neutral-700">{g.efeito}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-neutral-600">{labels.priority}:</span>
                            <Badge variant="primary" size="sm">{g.prioridade}</Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Badge variant={getRiskVariant(g.risco)} size="sm">
                      {labels.risks[g.risco as keyof typeof labels.risks]}
                    </Badge>
                  </div>
                </div>
              )
            })}
        </div>
      </Section>

      <Section id="recommendations" title={labels.recommendations}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {(['faang', 'consultoria', 'geral'] as const).map((k) => {
            const icons = {
              faang: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
              consultoria: "M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 0H8m8 0v6a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2H10a2 2 0 00-2 2v2",
              geral: "M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
            }

            return (
              <div key={k} className="ra-card p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 rounded-xl bg-primary-100 flex items-center justify-center">
                    <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={icons[k]} />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-800">{labels.recKeys[k]}</h3>
                </div>
                <ul className="space-y-3">
                  {data.recomendacoes[k].map((it, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <div className="w-2 h-2 rounded-full bg-primary-400 mt-2 flex-shrink-0"></div>
                      <span className="text-neutral-700 leading-relaxed">{it}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )
          })}
        </div>
      </Section>

      <Section id="bullets" title={labels.bullets}>
        <div className="mb-6 p-4 bg-neutral-50 rounded-xl border border-neutral-200">
          <div className="flex items-center gap-2 text-sm text-neutral-600">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Each bullet point shows the original text, improved version, identified issues, and detailed rationale.</span>
          </div>
        </div>

        <div className="space-y-6" role="list">
          {data.bullets_reescritos.map((b, idx) => {
            const issueCount = b.problemas.length

            return (
              <div key={idx} className="ra-card p-6 hover:shadow-lg transition-all duration-300" role="listitem">
                {/* Header with bullet number */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                    <span className="text-lg font-bold text-primary-600">{idx + 1}</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-neutral-800">Bullet Point {idx + 1}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="primary" size="sm">{issueCount} {issueCount === 1 ? 'issue' : 'issues'} identified</Badge>
                    </div>
                  </div>
                </div>

                {/* Main content grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  {/* Original text */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-neutral-200 flex items-center justify-center">
                        <svg className="w-3 h-3 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-neutral-700">{labels.original}</span>
                    </div>
                    <div className="bg-neutral-50 border border-neutral-200 rounded-xl p-4">
                      <p className="text-neutral-700 leading-relaxed">{b.original}</p>
                    </div>
                  </div>

                  {/* Rewritten text */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-success-100 flex items-center justify-center">
                        <svg className="w-3 h-3 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-success-700">{labels.rewritten}</span>
                    </div>
                    <div className="bg-gradient-to-br from-success-50 to-success-100/50 border border-success-200 rounded-xl p-4">
                      <p className="text-success-900 font-medium leading-relaxed">{b.reescrito}</p>
                    </div>
                  </div>
                </div>

                {/* Issues section */}
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-6 h-6 rounded-full bg-warning-100 flex items-center justify-center">
                      <svg className="w-3 h-3 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-warning-700">{labels.issues} Identified</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {b.problemas.map((p, i) => (
                      <Badge key={i} variant="warning" size="sm" className="hover:scale-105 transition-transform">{p}</Badge>
                    ))}
                  </div>
                </div>

                {/* Rationale section */}
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center">
                      <svg className="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <span className="text-sm font-semibold text-primary-700">{labels.rationale}</span>
                  </div>
                  <div className="bg-primary-50 border border-primary-200 rounded-xl p-4">
                    <p className="text-primary-900 leading-relaxed">{b.racional}</p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </Section>

      <Section id="keywords" title={labels.keywords}>
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-primary-100 flex items-center justify-center">
              <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-neutral-800">Suggested Keywords</h3>
              <p className="text-sm text-neutral-600">Strategic keywords to enhance your resume visibility</p>
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            {data.palavras_chave_sugeridas.map((kw, idx) => (
              <Badge key={idx} variant="primary" size="md" className="hover:scale-105 transition-transform cursor-pointer">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                {kw}
              </Badge>
            ))}
          </div>
        </div>
      </Section>

      <Section id="clarifications" title={labels.clarifications}>
        <div className="space-y-4">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-warning-100 flex items-center justify-center">
              <svg className="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-neutral-800">Pending Clarifications</h3>
              <p className="text-sm text-neutral-600">Questions that need additional information</p>
            </div>
          </div>

          <div className="space-y-3">
            {data.clarificacoes_pendentes.map((q, idx) => (
              <div key={idx} className="ra-card p-4 border-l-4 border-warning-400 bg-warning-50/50">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-warning-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-bold text-warning-600">?</span>
                  </div>
                  <p className="text-neutral-800 leading-relaxed">{q}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Section>

      <Section id="notes" title={labels.additionalNotes}>
        <div className="ra-card bg-gradient-to-br from-neutral-50 to-neutral-100/50 p-6 border border-neutral-200">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-xl bg-neutral-200 flex items-center justify-center flex-shrink-0">
              <svg className="w-6 h-6 text-neutral-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-neutral-800 mb-3">Additional Notes</h3>
              <div className="prose prose-neutral max-w-none">
                <p className="text-neutral-700 leading-relaxed whitespace-pre-line">{data.observacoes_adicionais}</p>
              </div>
            </div>
          </div>
        </div>
      </Section>
    </div>
  )
}
