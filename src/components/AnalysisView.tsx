import { useMemo } from 'react'
import { Section } from './Section'
import { ScoreBar } from './ScoreBar'
import { Badge } from './Badge'
import { detectLocale, riskColor, t } from '../i18n'
import type { ResumeAnalysis } from '../types'

export function AnalysisView({ data }: { data: ResumeAnalysis }) {
  const locale = useMemo(() => detectLocale(data), [data])
  const labels = useMemo(() => t(locale), [locale])

  return (
    <div className="space-y-10">
      <Section id="summary" title={labels.executiveSummary}>
        <p className="text-gray-800 leading-relaxed whitespace-pre-line">{data.resumo_executivo}</p>
      </Section>

      <Section id="scoring" title={labels.scoring}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <ScoreBar label={labels.scores.fit_faang} value={data.pontuacao.fit_faang} />
            <ScoreBar label={labels.scores.fit_consultoria} value={data.pontuacao.fit_consultoria} />
            <ScoreBar label={labels.scores.robustez_metrica} value={data.pontuacao.robustez_metrica} />
            <ScoreBar label={labels.scores.score_final} value={data.pontuacao.score_final} max={10} />
          </div>
          <div className="rounded-lg bg-gray-50 p-4 text-sm text-gray-700">
            <div className="font-medium mb-1">{labels.scores.score_final}: {data.pontuacao.score_final.toFixed(1)}</div>
            <p className="text-gray-700">{data.pontuacao.justificativa}</p>
          </div>
        </div>
      </Section>

      <Section id="strengths" title={labels.strengths}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(['impacto','lideranca','tecnico_analitico','negocio_estrategia','diferenciais'] as const).map((key) => {
            const items = data.pontos_fortes[key]
            return (
              <div key={key} className="space-y-2">
                <h3 className="text-sm font-semibold text-gray-700">{labels.categories[key]}</h3>
                <ul className="list-disc pl-5 space-y-1 text-sm text-gray-800">
                  {items.map((it: string, idx: number) => (
                    <li key={idx}>{it}</li>
                  ))}
                </ul>
              </div>
            )
          })}
        </div>
      </Section>

      <Section id="gaps" title={labels.gaps}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {data.gaps_prioritarios
            .slice()
            .sort((a, b) => a.prioridade - b.prioridade)
            .map((g, idx) => (
              <div key={idx} className="rounded-lg border border-gray-200 p-4 space-y-2">
                <div className="flex items-start justify-between gap-2">
                  <h4 className="font-medium text-gray-900">{g.gap}</h4>
                  <Badge className={riskColor(g.risco)}>{labels.risks[g.risco as keyof typeof labels.risks]}</Badge>
                </div>
                <p className="text-sm text-gray-700"><span className="font-medium">{labels.effect}:</span> {g.efeito}</p>
                <p className="text-xs text-gray-500">{labels.priority}: {g.prioridade}</p>
              </div>
            ))}
        </div>
      </Section>

      <Section id="recommendations" title={labels.recommendations}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {(['faang', 'consultoria', 'geral'] as const).map((k) => (
            <div key={k} className="space-y-2">
              <h3 className="text-sm font-semibold text-gray-700">{labels.recKeys[k]}</h3>
              <ul className="list-disc pl-5 space-y-1 text-sm text-gray-800">
                {data.recomendacoes[k].map((it, idx) => (
                  <li key={idx}>{it}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </Section>

      <Section id="bullets" title={labels.bullets}>
  <p className="text-xs text-gray-500 mb-2">Click a row to expand, view rationale & issues.</p>
        <div className="flex flex-col divide-y divide-gray-200" role="list">
          {data.bullets_reescritos.map((b, idx) => {
            const issueCount = b.problemas.length
            const issueTone = issueCount > 3 ? 'bg-rose-100 text-rose-700 border-rose-200' : issueCount > 1 ? 'bg-amber-100 text-amber-700 border-amber-200' : 'bg-emerald-100 text-emerald-700 border-emerald-200'
            return (
              <details key={idx} className="group py-3 focus:outline-none open:pb-5 transition-colors focus-visible:ring-2 focus-visible:ring-emerald-500/60" role="listitem">
                <summary className="cursor-pointer list-none flex flex-col gap-3 md:flex-row md:items-start md:justify-between" aria-label={`Bullet ${idx + 1}: ${b.original.substring(0,80)}${b.original.length>80?'…':''}`}> 
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 text-xs font-semibold tracking-wide text-gray-500">
                      <span className="inline-block h-2 w-2 rounded-full bg-emerald-500 group-open:bg-emerald-600 transition-colors" />
                      <span>{labels.original}</span>
                      <span className={`inline-flex items-center gap-1 border px-1.5 py-0.5 rounded-full text-[10px] font-medium ${issueTone}`}>{issueCount}<span className="sr-only"> issues</span></span>
                      <span className="ml-1 text-gray-400 text-[10px] group-open:hidden">(expand)</span>
                    </div>
                    <p className="mt-1 text-sm text-gray-800 line-clamp-2 group-open:line-clamp-none transition-all pr-2">{b.original}</p>
                  </div>
                  <div className="relative mt-1 md:mt-0 md:w-1/2 ra-card p-4 hoverable bg-white/60 backdrop-blur-sm">
                    <div className="text-xs uppercase tracking-wide text-gray-500 mb-1 flex items-center justify-between">
                      <span>{labels.rewritten}</span>
                      <span className="text-[10px] text-gray-400 font-normal hidden md:inline group-open:hidden">{labels.rationale}</span>
                    </div>
                    <p className="text-sm text-gray-900 font-medium">{b.reescrito}</p>
                    <p className="mt-2 text-[11px] text-gray-600 hidden md:block"><span className="font-medium">{labels.rationale}:</span> {b.racional}</p>
                    <div className="absolute top-2 right-2 text-gray-400 group-open:rotate-180 transition-transform" aria-hidden="true">⌄</div>
                  </div>
                </summary>
                <div className="mt-3 flex flex-wrap gap-2">
                  {b.problemas.map((p, i) => (
                    <Badge key={i} className="bg-orange-50 text-orange-700 border-orange-200 hover:scale-[1.03] transition-transform">{p}</Badge>
                  ))}
                </div>
                <p className="mt-3 text-xs text-gray-600 md:hidden"><span className="font-medium">{labels.rationale}:</span> {b.racional}</p>
              </details>
            )
          })}
        </div>
      </Section>

      <Section id="keywords" title={labels.keywords}>
        <div className="flex flex-wrap gap-2">
          {data.palavras_chave_sugeridas.map((kw, idx) => (
            <Badge key={idx} className="bg-gray-100 text-gray-800 border-gray-200">{kw}</Badge>
          ))}
        </div>
      </Section>

      <Section id="clarifications" title={labels.clarifications}>
        <ul className="list-disc pl-5 space-y-2 text-sm text-gray-800">
          {data.clarificacoes_pendentes.map((q, idx) => (
            <li key={idx}>{q}</li>
          ))}
        </ul>
      </Section>

      <Section id="notes" title={labels.additionalNotes}>
        <p className="text-gray-800">{data.observacoes_adicionais}</p>
      </Section>
    </div>
  )
}
