import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Triage } from './Triage'
import { ResumeProvider } from '../context/ResumeContext'

const validJson = JSON.stringify({
  resumo_executivo: 'x',
  pontuacao: { fit_faang:1, fit_consultoria:1, robustez_metrica:1, score_final:1, justificativa:'ok' },
  pontos_fortes: { impacto:[], lideranca:[], tecnico_analitico:[], negocio_estrategia:[], diferenciais:[] },
  gaps_prioritarios: [],
  recomendacoes: { faang:[], consultoria:[], geral:[] },
  bullets_reescritos: [],
  palavras_chave_sugeridas: [],
  clarificacoes_pendentes: [],
  observacoes_adicionais: ''
})

describe('Triage', () => {
  it('shows error for non-json extension', async () => {
    render(<ResumeProvider><Triage onLoaded={()=>{}} /></ResumeProvider>)
    const file = new File([validJson], 'data.txt', { type: 'text/plain' })
  const hidden = screen.getAllByTestId('hidden-input')[0] as HTMLInputElement
    fireEvent.change(hidden, { target: { files: [file] } })
    await waitFor(() => {
      expect(screen.getByText(/File must have \.json extension/i)).toBeTruthy()
    })
  })
  it('accepts valid JSON', async () => {
    render(<ResumeProvider><Triage onLoaded={()=>{}} /></ResumeProvider>)
  const hidden = screen.getAllByTestId('hidden-input')[0] as HTMLInputElement
    const file = new File([validJson], 'data.json', { type: 'application/json' })
    fireEvent.change(hidden, { target: { files: [file] } })
    await waitFor(() => {
      expect(screen.getByText(/Analysis loaded/i)).toBeTruthy()
    })
  })
})
