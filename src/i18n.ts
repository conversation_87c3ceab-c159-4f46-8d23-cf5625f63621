import type { ResumeAnalysis, RiskLevel } from './types'

export type Locale = 'pt-BR' | 'en-US'

export function detectLocale(data: ResumeAnalysis): Locale {
  const text = JSON.stringify(data).toLowerCase()
  const ptHints = ['á', 'ã', 'ê', 'ção', 'métric', 'pontua', 'consultoria', 'justificativa', 'lideran', 'negócio', 'prioridade']
  const isPt = ptHints.some((h) => text.includes(h))
  return isPt ? 'pt-BR' : 'en-US'
}

export const t = (locale: Locale) => {
  const labels = {
    'pt-BR': {
      executiveSummary: 'Resumo executivo',
      scoring: 'Pontuação',
      strengths: 'Pontos fortes',
      gaps: 'Gaps prioritários',
      recommendations: 'Recomendações',
      bullets: 'Bullets reescritos',
      keywords: 'Palavras‑chave sugeridas',
      clarifications: 'Clarezas pendentes',
      additionalNotes: 'Observações adicionais',
      scores: {
        fit_faang: 'Fit FAANG',
        fit_consultoria: 'Fit Consultoria',
        robustez_metrica: '<PERSON>ustez de Métrica',
        score_final: 'Score Final'
      },
      original: 'Original',
      rewritten: 'Reescrito',
      issues: 'Problemas',
      rationale: 'Racional',
      risk: 'Risco',
      effect: 'Efeito',
      priority: 'Prioridade',
      categories: {
        impacto: 'Impacto',
        lideranca: 'Liderança',
        tecnico_analitico: 'Técnico/Analítico',
        negocio_estrategia: 'Negócio/Estratégia',
        diferenciais: 'Diferenciais',
      },
      recKeys: { faang: 'FAANG', consultoria: 'Consultoria', geral: 'Geral' },
      risks: { alto: 'Alto', medio: 'Médio', baixo: 'Baixo', 'médio': 'Médio' },
    },
    'en-US': {
      executiveSummary: 'Executive summary',
      scoring: 'Scoring',
      strengths: 'Strengths',
      gaps: 'Priority gaps',
      recommendations: 'Recommendations',
      bullets: 'Rewritten bullets',
      keywords: 'Suggested keywords',
      clarifications: 'Open clarifications',
      additionalNotes: 'Additional notes',
      scores: {
        fit_faang: 'FAANG fit',
        fit_consultoria: 'Consulting fit',
        robustez_metrica: 'Metric robustness',
        score_final: 'Final score'
      },
      original: 'Original',
      rewritten: 'Rewritten',
      issues: 'Issues',
      rationale: 'Rationale',
      risk: 'Risk',
      effect: 'Effect',
      priority: 'Priority',
      categories: {
        impacto: 'Impact',
        lideranca: 'Leadership',
        tecnico_analitico: 'Technical/Analytical',
        negocio_estrategia: 'Business/Strategy',
        diferenciais: 'Differentials',
      },
      recKeys: { faang: 'FAANG', consultoria: 'Consulting', geral: 'General' },
      risks: { alto: 'High', medio: 'Medium', baixo: 'Low', 'médio': 'Medium' },
    },
  } as const
  return labels[locale]
}

export function riskColor(level: RiskLevel): string {
  const l = level.toLowerCase()
  if (l.startsWith('alto')) return 'ra-badge-risk-high'
  if (l.startsWith('med')) return 'ra-badge-risk-medium'
  return 'ra-badge-risk-low'
}

