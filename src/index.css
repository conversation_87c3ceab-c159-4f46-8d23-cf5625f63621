@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: light;

  /* Modern Design Tokens */
  --ra-radius-sm: 6px;
  --ra-radius: 16px;
  --ra-radius-lg: 24px;
  --ra-radius-xl: 32px;

  /* Enhanced Shadows */
  --ra-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --ra-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --ra-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --ra-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ra-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Smooth Transitions */
  --ra-transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --ra-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --ra-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Modern Color Palette */
  --ra-primary-50: #eff6ff;
  --ra-primary-100: #dbeafe;
  --ra-primary-500: #3b82f6;
  --ra-primary-600: #2563eb;
  --ra-primary-700: #1d4ed8;
  --ra-primary-800: #1e40af;
  --ra-primary-900: #1e3a8a;

  --ra-success-50: #ecfdf5;
  --ra-success-100: #d1fae5;
  --ra-success-500: #10b981;
  --ra-success-600: #059669;
  --ra-success-700: #047857;
  --ra-success-800: #065f46;

  --ra-warning-50: #fffbeb;
  --ra-warning-100: #fef3c7;
  --ra-warning-500: #f59e0b;
  --ra-warning-600: #d97706;
  --ra-warning-700: #b45309;
  --ra-warning-800: #92400e;

  --ra-danger-50: #fef2f2;
  --ra-danger-100: #fee2e2;
  --ra-danger-500: #ef4444;
  --ra-danger-600: #dc2626;
  --ra-danger-700: #b91c1c;
  --ra-danger-800: #991b1b;

  --ra-neutral-50: #f8fafc;
  --ra-neutral-100: #f1f5f9;
  --ra-neutral-200: #e2e8f0;
  --ra-neutral-300: #cbd5e1;
  --ra-neutral-400: #94a3b8;
  --ra-neutral-500: #64748b;
  --ra-neutral-600: #475569;
  --ra-neutral-700: #334155;
  --ra-neutral-800: #1e293b;
  --ra-neutral-900: #0f172a;
}

html, body, #root {
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

body {
  @apply bg-neutral-50 text-neutral-900;
  background: linear-gradient(135deg, var(--ra-neutral-50) 0%, #ffffff 100%);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  letter-spacing: -0.01em;
}

/* Enhanced Typography System */
h1, .ra-h1 {
  @apply text-4xl md:text-5xl font-bold tracking-tight;
  color: var(--ra-neutral-900);
  line-height: 1.1;
}

h2, .ra-h2 {
  @apply text-2xl md:text-3xl font-semibold tracking-tight;
  color: var(--ra-neutral-800);
  line-height: 1.2;
}

h3, .ra-h3 {
  @apply text-xl font-semibold;
  color: var(--ra-neutral-800);
  line-height: 1.3;
}

/* Modern Card System */
.ra-card {
  @apply bg-white rounded-2xl border border-neutral-200/60;
  box-shadow: var(--ra-shadow);
  transition: all var(--ra-transition);
  backdrop-filter: blur(8px);
}

.ra-card.hoverable:hover {
  box-shadow: var(--ra-shadow-lg);
  transform: translateY(-1px);
  border-color: var(--ra-primary-200);
}

.ra-card.elevated {
  box-shadow: var(--ra-shadow-xl);
  border-color: var(--ra-primary-100);
}

/* Enhanced Focus States & Accessibility */
:focus-visible {
  outline: 3px solid var(--ra-primary-600);
  outline-offset: 2px;
  border-radius: var(--ra-radius-sm);
  transition: outline-color var(--ra-transition);
}

button:focus-visible, a:focus-visible {
  @apply ring-2 ring-offset-2 ring-offset-white;
  --tw-ring-color: var(--ra-primary-600);
  box-shadow: 0 0 0 2px var(--ra-primary-600), 0 0 0 4px rgba(37, 99, 235, 0.2);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --ra-primary-600: #1e40af;
    --ra-neutral-900: #000000;
    --ra-neutral-100: #ffffff;
  }

  .ra-card {
    border-width: 2px;
    border-color: var(--ra-neutral-900);
  }

  button, .ra-btn {
    border-width: 2px;
    font-weight: 700;
  }
}

/* Focus management for interactive elements */
details[open] summary {
  outline: 2px solid var(--ra-primary-600);
  outline-offset: 2px;
}

/* Keyboard navigation improvements */
.ra-btn:focus-visible,
button:focus-visible {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px var(--ra-primary-600), 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Screen reader improvements */
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0.5rem 1rem;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  background: var(--ra-primary-600);
  color: white;
  border-radius: var(--ra-radius);
  font-weight: 600;
  z-index: 9999;
}

/* Skip link styling */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--ra-primary-600);
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: var(--ra-radius);
  font-weight: 600;
  z-index: 1000;
  transition: top var(--ra-transition);
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced Animations */
.fade-in {
  animation: fade-in 0.5s var(--ra-transition);
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.slide-in-right {
  animation: slide-in-right 0.3s var(--ra-transition);
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(16px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Screen reader only helper */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0,0,0,0);
  white-space: nowrap;
  border: 0;
}

/* Modern Risk Badge System */
.ra-badge-risk-low {
  background: var(--ra-success-50);
  color: var(--ra-success-800);
  border: 1px solid var(--ra-success-200);
  font-weight: 500;
}

.ra-badge-risk-medium {
  background: var(--ra-warning-50);
  color: var(--ra-warning-800);
  border: 1px solid var(--ra-warning-200);
  font-weight: 500;
}

.ra-badge-risk-high {
  background: var(--ra-danger-50);
  color: var(--ra-danger-800);
  border: 1px solid var(--ra-danger-200);
  font-weight: 500;
}

/* Enhanced Button System */
.ra-btn {
  @apply inline-flex items-center justify-center rounded-xl px-4 py-2.5 text-sm font-medium transition-all;
  transition: all var(--ra-transition);
}

.ra-btn-primary {
  background: var(--ra-primary-600);
  color: white;
  border: 1px solid var(--ra-primary-600);
}

.ra-btn-primary:hover {
  background: var(--ra-primary-700);
  border-color: var(--ra-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--ra-shadow-md);
}

.ra-btn-secondary {
  background: white;
  color: var(--ra-neutral-700);
  border: 1px solid var(--ra-neutral-300);
}

.ra-btn-secondary:hover {
  background: var(--ra-neutral-50);
  border-color: var(--ra-neutral-400);
  transform: translateY(-1px);
  box-shadow: var(--ra-shadow);
}

/* Enhanced Progress Bar System */
.ra-progress-high {
  background: linear-gradient(135deg, var(--ra-success-500), var(--ra-success-400));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.ra-progress-medium {
  background: linear-gradient(135deg, var(--ra-warning-500), var(--ra-warning-400));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.ra-progress-low {
  background: linear-gradient(135deg, var(--ra-danger-500), var(--ra-danger-400));
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Responsive Optimizations */
@media (max-width: 360px) {
  body {
    font-size: 15px;
    letter-spacing: -0.005em;
  }

  nav a, nav button {
    letter-spacing: 0;
    font-size: 13px;
  }

  .ra-card {
    border-radius: var(--ra-radius);
    margin: 0.5rem;
  }

  .space-y-10 > :where(:not([hidden]) ~ :not([hidden])) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  }

  details.group {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .h-14 {
    height: 3.5rem;
  }
}

/* Enhanced Mobile Experience */
@media (max-width: 768px) {
  .ra-card {
    margin-left: 0.75rem;
    margin-right: 0.75rem;
    border-radius: var(--ra-radius);
  }

  h1, .ra-h1 {
    font-size: 2rem;
    line-height: 1.1;
  }

  h2, .ra-h2 {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  /* Touch-friendly interactions */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved tap targets */
  .ra-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  /* Better spacing for mobile */
  .space-y-12 > :where(:not([hidden]) ~ :not([hidden])) {
    margin-top: 2rem;
  }

  /* Optimized grid layouts */
  .grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .grid-cols-1.lg\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Better grid layouts for tablets */
  .grid-cols-1.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Optimized card spacing */
  .ra-card {
    padding: 1.5rem;
  }
}

/* Large screen optimizations */
@media (min-width: 1280px) {
  .container {
    max-width: 1200px;
  }

  /* Enhanced spacing for large screens */
  .space-y-12 > :where(:not([hidden]) ~ :not([hidden])) {
    margin-top: 4rem;
  }

  .ra-card {
    padding: 2rem;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .ra-card {
    border-width: 0.5px;
  }

  /* Sharper shadows on high DPI */
  .ra-card {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.12), 0 1px 2px -1px rgb(0 0 0 / 0.12);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .fade-in {
    animation: none;
  }

  .slide-in-right {
    animation: none;
  }
}

/* Performance optimizations */
.ra-card {
  contain: layout style;
  will-change: transform, box-shadow;
}

.ra-card.hoverable {
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Optimize font rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Print styles */
@media print {
  .ra-card {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    break-inside: avoid;
  }

  nav,
  button,
  .fixed {
    display: none !important;
  }

  body {
    background: white !important;
  }

  .space-y-12 > :where(:not([hidden]) ~ :not([hidden])) {
    margin-top: 1rem !important;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  :root {
    --ra-neutral-50: #0f172a;
    --ra-neutral-100: #1e293b;
    --ra-neutral-200: #334155;
    --ra-neutral-900: #f8fafc;
  }

  /* Note: Full dark mode implementation would require more extensive changes */
}


