@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  color-scheme: light;
  --ra-radius-sm: 4px;
  --ra-radius: 12px;
  --ra-radius-lg: 20px;
  --ra-shadow: 0 1px 2px -1px rgb(0 0 0 / 0.08), 0 2px 4px -2px rgb(0 0 0 / 0.06);
  --ra-shadow-hover: 0 2px 4px -2px rgb(0 0 0 / 0.12), 0 4px 8px -2px rgb(0 0 0 / 0.10);
  --ra-transition: 140ms cubic-bezier(.4,0,.2,1);
  /* High-contrast palette tokens (accessible on light bg) */
  --ra-c-emerald-soft: #065f46; /* Emerald 800 */
  --ra-c-amber-soft: #92400e;   /* Amber 800 */
  --ra-c-rose-soft: #9f1239;    /* Rose 800 */
}

html, body, #root {
  height: 100%;
}

body {
  @apply bg-gray-50 text-gray-900;
}

/* Typography scale adjustments */
h1, .ra-h1 { @apply text-3xl md:text-4xl font-semibold tracking-tight; }
h2, .ra-h2 { @apply text-2xl md:text-3xl font-semibold tracking-tight; }
h3, .ra-h3 { @apply text-xl font-semibold; }

/* Card primitive */
.ra-card { @apply bg-white rounded-xl shadow-sm ring-1 ring-gray-200; box-shadow: var(--ra-shadow); transition: box-shadow var(--ra-transition), transform var(--ra-transition); }
.ra-card.hoverable:hover { box-shadow: var(--ra-shadow-hover); transform: translateY(-2px); }

/* Focus outline unification */
:focus-visible { outline: 2px solid theme('colors.gray.900'); outline-offset: 2px; }
button:focus-visible, a:focus-visible { @apply ring-2 ring-gray-900 ring-offset-2 ring-offset-white; }

/* Smooth section reveal */
.fade-in { animation: fade-in .4s var(--ra-transition); }
@keyframes fade-in { from { opacity: 0; transform: translateY(4px);} to { opacity:1; transform: translateY(0);} }

/* Screen reader only helper (Tailwind normally provides .sr-only, but ensure availability) */
.sr-only { position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0; }

/* Contrast helpers for risk / status badges */
.ra-badge-risk-low { background:#ecfdf5; color:#065f46; border:1px solid #6ee7b7; }
.ra-badge-risk-medium { background:#fffbeb; color:#92400e; border:1px solid #fcd34d; }
.ra-badge-risk-high { background:#fff1f2; color:#9f1239; border:1px solid #fda4af; }

/* Ensure minimum tap target & tighter layout on ultra-small devices */
@media (max-width: 360px) {
  body { font-size: 15px; }
  nav a, nav button { letter-spacing: 0; }
  .ra-card { border-radius: var(--ra-radius-sm); }
  .space-y-10 > :where(:not([hidden]) ~ :not([hidden])) { --tw-space-y-reverse: 0; margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse))); }
  /* Bullet accordion paddings */
  details.group { padding-left: .25rem; padding-right: .25rem; }
  .h-14 { height: 3rem; }
}

/* Improve progress bar contrast when gradient is narrow */
.progress-bg-high { background: linear-gradient(to right,#059669,#10b981); }
.progress-bg-medium { background: linear-gradient(to right,#d97706,#f59e0b); }
.progress-bg-low { background: linear-gradient(to right,#e11d48,#f43f5e); }


