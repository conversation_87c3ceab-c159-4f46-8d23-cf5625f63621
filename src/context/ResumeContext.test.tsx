import { describe, it, expect } from 'vitest'
import { basicValidate } from './ResumeContext'

const valid = {
  resumo_executivo: '',
  pontuacao: { fit_faang:1, fit_consultoria:1, robustez_metrica:1, score_final:1, justificativa:'' },
  pontos_fortes: { impacto:[], lideranca:[], tecnico_analitico:[], negocio_estrategia:[], diferenciais:[] },
  gaps_prioritarios: [],
  recomendacoes: { faang:[], consultoria:[], geral:[] },
  bullets_reescritos: [],
  palavras_chave_sugeridas: [],
  clarificacoes_pendentes: [],
  observacoes_adicionais: ''
}

describe('basicValidate', () => {
  it('passes valid structure', () => {
  expect(basicValidate(valid).errors).toHaveLength(0)
  })
  it('detects missing root fields', () => {
    const clone: any = { ...valid }
    delete clone.resumo_executivo
  const { errors } = basicValidate(clone)
  expect(errors.some(e=>e.includes('resumo_executivo'))).toBe(true)
  })
  it('detects nested missing pontuacao fields', () => {
    const clone: any = { ...valid, pontuacao: { fit_faang:1 } }
  const { errors } = basicValidate(clone)
  expect(errors.some(e=>e.includes('pontuacao.fit_consultoria'))).toBe(true)
  })
})
