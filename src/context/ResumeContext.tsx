import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import type { ResumeAnalysis } from '../types'
import { saveRaw, loadRaw } from '../utils/storage'

interface LoadResult { ok: boolean; errors?: string[]; warnings?: string[] }
interface ResumeContextValue {
  data: ResumeAnalysis | null
  setData: (data: ResumeAnalysis | null) => void
  loadFromString: (json: string) => LoadResult
}

const ResumeContext = createContext<ResumeContextValue | undefined>(undefined)

export function basicValidate(obj: any): { errors: string[]; warnings: string[] } {
  const errors: string[] = []
  const warnings: string[] = []
  if (typeof obj !== 'object' || obj === null) return { errors: ['Root is not an object'], warnings: [] }
  const rootReq: (keyof import('../types').ResumeAnalysis)[] = [
    'resumo_executivo','pontuacao','pontos_fortes','gaps_prioritarios','recomendacoes','bullets_reescritos','palavras_chave_sugeridas','clarificacoes_pendentes','observacoes_adicionais'
  ]
  for (const k of rootReq) if (!(k in obj)) errors.push(`Missing field: ${k}`)
  // unknown root keys -> warnings
  Object.keys(obj).forEach(key => {
    if (!rootReq.includes(key as any)) warnings.push(`Unknown root field: ${key}`)
  })
  if (obj.pontuacao) {
    const pReq = ['fit_faang','fit_consultoria','robustez_metrica','score_final','justificativa']
    for (const k of pReq) if (!(k in obj.pontuacao)) errors.push(`pontuacao.${k} missing`)
  }
  if (obj.pontos_fortes) {
    const cReq = ['impacto','lideranca','tecnico_analitico','negocio_estrategia','diferenciais']
    for (const k of cReq) if (!(k in obj.pontos_fortes)) errors.push(`pontos_fortes.${k} missing`)
  }
  return { errors, warnings }
}

export const ResumeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [data, setData] = useState<ResumeAnalysis | null>(null)

  // load from storage once
  useEffect(() => {
    // 1) Legacy direct injection support (e.g., server-side script sets window.__RESUME_ANALYSIS__)
    // 2) Fallback to localStorage persistence
    let initialized = false
    try {
      const anyWin = window as any
      if (anyWin.__RESUME_ANALYSIS__) {
        const candidate = anyWin.__RESUME_ANALYSIS__
  const { errors: errs } = basicValidate(candidate)
  if (!errs.length) {
          setData(candidate as ResumeAnalysis)
          initialized = true
        }
      }
    } catch {}
    if (!initialized) {
      const stored = loadRaw()
      if (stored) {
        try {
          const parsed = JSON.parse(stored)
          const { errors: errs } = basicValidate(parsed)
          if (!errs.length) setData(parsed as ResumeAnalysis)
        } catch {}
      }
    }
  }, [])

  const loadFromString = useCallback((json: string): LoadResult => {
    try {
      const parsed = JSON.parse(json)
      const { errors, warnings } = basicValidate(parsed)
      if (errors.length) return { ok: false, errors, warnings }
      setData(parsed as ResumeAnalysis)
  saveRaw(json)
      return { ok: true, warnings }
    } catch (e: any) {
      return { ok: false, errors: [e.message || 'Invalid JSON'] }
    }
  }, [])

  return (
    <ResumeContext.Provider value={{ data, setData, loadFromString }}>
      {children}
    </ResumeContext.Provider>
  )
}

export function useResume() {
  const ctx = useContext(ResumeContext)
  if (!ctx) throw new Error('useResume must be used within ResumeProvider')
  return ctx
}
