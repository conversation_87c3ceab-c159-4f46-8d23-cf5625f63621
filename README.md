# Resume Analyzer

Upload a JSON resume analysis export and get an interactive, sectioned visualization (scores, strengths, gaps, rewritten bullets, recommendations, keywords, clarifications and notes).

## Core Flow

1. Landing (triage) presents drag & drop / file picker (.json only)
2. File is parsed & schema‑validated client‑side (no network request)
3. On success, analysis sections render instantly and persist in localStorage
4. You can clear and re-upload at any time

## Expected JSON Schema (fields required)

```json
{
  "resumo_executivo": string,
  "pontuacao": {
    "fit_faang": number,
    "fit_consultoria": number,
    "robustez_metrica": number,
    "score_final": number,
    "justificativa": string
  },
  "pontos_fortes": {
    "impacto": string[],
    "lideranca": string[],
    "tecnico_analitico": string[],
    "negocio_estrategia": string[],
    "diferenciais": string[]
  },
  "gaps_prioritarios": [{ "gap": string, "risco": string, "efeito": string, "prioridade": number }],
  "recomendacoes": { "faang": string[], "consultoria": string[], "geral": string[] },
  "bullets_reescritos": [{ "original": string, "problemas": string[], "reescrito": string, "racional": string }],
  "palavras_chave_sugeridas": string[],
  "clarificacoes_pendentes": string[],
  "observacoes_adicionais": string
}
```

## Development

Run locally:

```bash
npm install
npm run dev
```

Build:

```bash
npm run build
```

Tests:

```bash
npm run test
```

## Future Enhancements (ideas)

- Dark mode
- URL share (encoded state)
- JSON diff when re-uploading a new version
- Download sanitized JSON

## Accessibility & UX

- Keyboard-friendly navigation with active section highlighting
- Clear validation errors and success confirmation
- Responsive across mobile → desktop

